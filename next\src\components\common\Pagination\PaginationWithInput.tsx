import { Typography } from '@bratislava/component-library'
import { useTranslation } from 'next-i18next'
import React from 'react'

import Button from '@/src/components/common/Button/Button'
import Input from '@/src/components/common/Input/Input'
import { PaginationProps } from '@/src/components/common/Pagination/Pagination'
import { usePaginationWithInput } from '@/src/components/common/Pagination/usePaginationWithInput'
import cn from '@/src/utils/cn'

/**
 * Figma: https://www.figma.com/design/2qF09hDT9QNcpdztVMNAY4/OLO-Web?node-id=37-1906&t=Ix6vxd23ycmma0c2-4
 * Interaction design was inspired by: https://ant.design/components/pagination-cn
 */

const PaginationWithInput = ({
  currentPage,
  totalCount,
  onPageChange: handlePageChange,
}: PaginationProps) => {
  const { t } = useTranslation()

  const {
    inputValue,
    handleInputChange,
    handleDecrement,
    handleIncrement,
    handleBlur,
    handleKeyDown,
  } = usePaginationWithInput(currentPage, totalCount, handlePageChange)

  return (
    <nav>
      <div className={cn('flex items-center justify-start gap-4')}>
        <Button
          variant="category-plain"
          isDisabled={Number(inputValue) < 2}
          onPress={() => handleDecrement(Number(inputValue))}
          aria-label={t('Pagination.aria.goToPreviousPage', inputValue.toString())}
          icon={<Icon name="sipka-dolava" />}
          className="rounded-full"
        />

        <div className="flex items-center justify-center gap-2">
          <Input
            type="number"
            aria-label={t('Pagination.aria.goToPage', { page: inputValue })}
            value={inputValue}
            onChange={handleInputChange}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            className="items-center justify-center"
            classNameInner={cn('w-15 text-center', {
              // Widen the input field slightly for more than 3 digits
              'w-[4.37rem]': inputValue.toString().length > 3,
            })}
          />

          <div className="flex gap-1">
            <div className="flex size-6 justify-center">
              <Typography variant="p-default">/</Typography>
            </div>
            <Typography variant="p-default">{totalCount}</Typography>
          </div>
        </div>

        <Button
          variant="category-plain"
          isDisabled={Number(inputValue) >= totalCount || inputValue.toString() === ''}
          onPress={() => handleIncrement(Number(inputValue))}
          aria-label={t('Pagination.aria.goToNextPage', inputValue.toString())}
          icon={<Icon name="sipka-doprava" />}
          className="rounded-full"
        />
      </div>
    </nav>
  )
}

export default PaginationWithInput
